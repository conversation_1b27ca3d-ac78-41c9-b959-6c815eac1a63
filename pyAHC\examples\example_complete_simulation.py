#!/usr/bin/env python3
"""完整模拟工作流程示例

演示如何使用HDF5工作流程集成模块进行完整的项目模拟，包括：
1. 项目初始化
2. 日循环模拟
3. 项目汇总
4. 结果分析

这个示例展示了从配置到完成的完整工作流程。
"""

import logging
from datetime import date, datetime, timedelta
from pathlib import Path
import yaml
import sys
import os

# 添加pyAHC到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config_from_yaml(config_path: str) -> dict:
    """从YAML文件加载配置"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 转换日期字符串为date对象
        if 'start_date' in config:
            config['start_date'] = datetime.fromisoformat(config['start_date']).date()
        if 'end_date' in config:
            config['end_date'] = datetime.fromisoformat(config['end_date']).date()
        if 'planting_date' in config and config['planting_date']:
            config['planting_date'] = datetime.fromisoformat(config['planting_date']).date()
        
        return config
    except Exception as e:
        logger.error(f"Failed to load config from {config_path}: {e}")
        raise


def create_sample_config() -> dict:
    """创建示例配置"""
    config = {
        'hdf5_path': 'simulation_results.h5',
        'project_name': 'corn_field_001_2023',
        'start_date': date(2023, 5, 1),
        'end_date': date(2023, 9, 30),
        'field_id': 'field_001',
        'crop_type': 'corn',
        'variety': 'Pioneer_1234',
        'planting_date': date(2023, 5, 1),
        'latitude': 40.7128,
        'longitude': -74.0060,
        'altitude': 10.0,
        'enable_assimilation': False,
        'stop_on_error': True,
        'save_intermediate': True
    }
    return config


def run_simple_simulation():
    """运行简单的模拟示例"""
    logger.info("=== 开始简单模拟示例 ===")
    
    try:
        # 导入工作流程函数
        from pyahc.db.hdf5.workflow import (
            initialize_project_simulation,
            run_project_simulation
        )
        
        # 创建配置
        config = create_sample_config()
        
        # 调整为短期模拟（用于演示）
        config['start_date'] = date(2023, 5, 1)
        config['end_date'] = date(2023, 5, 7)  # 7天模拟
        config['project_name'] = 'demo_corn_week1'
        config['hdf5_path'] = 'demo_simulation.h5'
        
        logger.info(f"配置: {config['project_name']}")
        logger.info(f"模拟期间: {config['start_date']} 到 {config['end_date']}")
        
        # 运行完整模拟
        manager = run_project_simulation(config)
        
        logger.info("模拟完成！")
        
        # 显示结果摘要
        show_simulation_summary(manager, config['project_name'])
        
    except Exception as e:
        logger.error(f"模拟失败: {e}")
        raise


def run_step_by_step_simulation():
    """运行分步骤的模拟示例"""
    logger.info("=== 开始分步骤模拟示例 ===")
    
    try:
        from pyahc.db.hdf5.workflow import (
            initialize_project_simulation,
            daily_simulation_step,
            generate_project_summary,
            _create_base_model
        )
        
        # 创建配置
        config = create_sample_config()
        config['start_date'] = date(2023, 5, 1)
        config['end_date'] = date(2023, 5, 3)  # 3天模拟
        config['project_name'] = 'step_by_step_demo'
        config['hdf5_path'] = 'step_demo.h5'
        
        # 步骤1：初始化项目
        logger.info("步骤1：初始化项目")
        manager = initialize_project_simulation(config)
        
        # 步骤2：创建基础模型
        logger.info("步骤2：创建基础模型")
        base_model = _create_base_model(config)
        
        # 步骤3：执行日循环模拟
        logger.info("步骤3：执行日循环模拟")
        current_date = config['start_date']
        day_count = 0
        
        while current_date <= config['end_date']:
            logger.info(f"  模拟日期: {current_date}")
            
            result = daily_simulation_step(
                manager, config['project_name'], current_date, base_model
            )
            
            current_date += timedelta(days=1)
            day_count += 1
        
        # 步骤4：生成项目汇总
        logger.info("步骤4：生成项目汇总")
        generate_project_summary(
            manager, config['project_name'], 
            config['start_date'], config['end_date']
        )
        
        logger.info("分步骤模拟完成！")
        
        # 显示结果摘要
        show_simulation_summary(manager, config['project_name'])
        
    except Exception as e:
        logger.error(f"分步骤模拟失败: {e}")
        raise


def show_simulation_summary(manager, project_name: str):
    """显示模拟结果摘要"""
    logger.info("=== 模拟结果摘要 ===")
    
    try:
        with manager:
            # 显示项目元数据
            metadata = manager.get_project_metadata(project_name)
            logger.info(f"项目名称: {project_name}")
            logger.info(f"模拟期间: {metadata.get('simulation_period', {}).get('start_date')} 到 {metadata.get('simulation_period', {}).get('end_date')}")
            logger.info(f"总天数: {metadata.get('simulation_period', {}).get('total_days')}")
            
            # 显示统计信息
            project_group = manager._file[project_name]
            if 'summary' in project_group:
                statistics_group = project_group['summary']['statistics']
                logger.info("统计信息:")
                
                for key, value in statistics_group.attrs.items():
                    logger.info(f"  {key}: {value:.3f}")
            
            # 列出可用的日期数据
            available_dates = []
            for key in project_group.keys():
                if key.startswith('day_'):
                    available_dates.append(key)
            
            logger.info(f"可用数据日期: {len(available_dates)} 天")
            if available_dates:
                logger.info(f"  范围: {min(available_dates)} 到 {max(available_dates)}")
    
    except Exception as e:
        logger.error(f"显示摘要失败: {e}")


def run_with_yaml_config():
    """使用YAML配置文件运行模拟"""
    logger.info("=== 使用YAML配置文件运行模拟 ===")
    
    # 创建示例配置文件
    config_path = "example_config.yaml"
    create_example_yaml_config(config_path)
    
    try:
        # 加载配置
        config = load_config_from_yaml(config_path)
        
        # 导入工作流程函数
        from pyahc.db.hdf5.workflow import run_project_simulation
        
        # 运行模拟
        manager = run_project_simulation(config)
        
        logger.info("YAML配置模拟完成！")
        
        # 显示结果摘要
        show_simulation_summary(manager, config['project_name'])
        
    except Exception as e:
        logger.error(f"YAML配置模拟失败: {e}")
        raise


def create_example_yaml_config(config_path: str):
    """创建示例YAML配置文件"""
    config = {
        'hdf5_path': 'yaml_simulation.h5',
        'project_name': 'yaml_corn_demo',
        'start_date': '2023-05-01',
        'end_date': '2023-05-05',
        'field_id': 'yaml_field_001',
        'crop_type': 'corn',
        'variety': 'DKC_6789',
        'planting_date': '2023-05-01',
        'latitude': 41.8781,
        'longitude': -87.6298,
        'altitude': 180.0,
        'enable_assimilation': False,
        'stop_on_error': True,
        'save_intermediate': True
    }
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    logger.info(f"创建示例配置文件: {config_path}")


def main():
    """主函数"""
    logger.info("HDF5工作流程集成示例")
    
    try:
        # 示例1：简单模拟
        run_simple_simulation()
        
        # 示例2：分步骤模拟
        run_step_by_step_simulation()
        
        # 示例3：使用YAML配置
        run_with_yaml_config()
        
        logger.info("所有示例运行完成！")
        
    except ImportError as e:
        logger.error(f"导入错误: {e}")
        logger.error("请确保已正确安装pyAHC和相关依赖")
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        raise


if __name__ == "__main__":
    main()
