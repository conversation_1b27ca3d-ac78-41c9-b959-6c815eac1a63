"""数据同化接口测试模块

测试数据同化的各个组件：
- AssimilationManager
- ObservationDataLoader
- ObservationQualityController
- UncertaintyManager
"""

import unittest
import tempfile
import shutil
import numpy as np
import pandas as pd
from datetime import date
from pathlib import Path
import json
import logging

from pyahc.db.hdf5.assimilation import (
    AssimilationManager,
    ObservationDataLoader,
    ObservationQualityController,
    UncertaintyManager
)


class TestObservationDataLoader(unittest.TestCase):
    """测试观测数据加载器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'observation_dir': self.temp_dir
        }
        self.loader = ObservationDataLoader(self.config)
        
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_load_csv_observations(self):
        """测试CSV格式观测数据加载"""
        # 创建测试CSV文件
        csv_data = {
            'date': ['2013-05-01', '2013-05-02'],
            'lai': [0.5, 0.6],
            'biomass': [150.0, 180.0]
        }
        df = pd.DataFrame(csv_data)
        csv_file = Path(self.temp_dir) / 'obs_2013-05-01.csv'
        df.to_csv(csv_file, index=False)
        
        # 测试加载
        target_date = date(2013, 5, 1)
        observations = self.loader.load_observations_for_date(target_date)
        
        self.assertIsNotNone(observations)
        self.assertIn('lai', observations)
        self.assertIn('biomass', observations)
        self.assertEqual(observations['lai'], 0.5)
        self.assertEqual(observations['biomass'], 150.0)
    
    def test_load_json_observations(self):
        """测试JSON格式观测数据加载"""
        # 创建测试JSON文件
        json_data = {
            '2013-05-01': {
                'lai': 0.5,
                'biomass': 150.0
            }
        }
        json_file = Path(self.temp_dir) / 'obs_2013-05-01.json'
        with open(json_file, 'w') as f:
            json.dump(json_data, f)
        
        # 测试加载
        target_date = date(2013, 5, 1)
        observations = self.loader.load_observations_for_date(target_date)
        
        self.assertIsNotNone(observations)
        self.assertIn('lai', observations)
        self.assertIn('biomass', observations)
        self.assertEqual(observations['lai'], 0.5)
        self.assertEqual(observations['biomass'], 150.0)
    
    def test_load_txt_observations(self):
        """测试TXT格式观测数据加载"""
        # 创建测试TXT文件
        txt_content = """# 观测数据
lai=0.5
biomass=150.0
soil_moisture=0.25
"""
        txt_file = Path(self.temp_dir) / 'obs_2013-05-01.txt'
        with open(txt_file, 'w') as f:
            f.write(txt_content)
        
        # 测试加载
        target_date = date(2013, 5, 1)
        observations = self.loader.load_observations_for_date(target_date)
        
        self.assertIsNotNone(observations)
        self.assertIn('lai', observations)
        self.assertIn('biomass', observations)
        self.assertIn('soil_moisture', observations)
        self.assertEqual(observations['lai'], 0.5)
        self.assertEqual(observations['biomass'], 150.0)
        self.assertEqual(observations['soil_moisture'], 0.25)


class TestObservationQualityController(unittest.TestCase):
    """测试观测数据质量控制器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'quality_thresholds': {
                'lai': {'min': 0, 'max': 15},
                'biomass': {'min': 0, 'max': 50000}
            }
        }
        self.controller = ObservationQualityController(self.config)
    
    def test_validate_valid_observations(self):
        """测试有效观测数据验证"""
        observations = {
            'lai': 2.5,
            'biomass': 1000.0
        }
        
        validated = self.controller.validate_observations(observations)
        
        self.assertEqual(len(validated), 2)
        self.assertIn('lai', validated)
        self.assertIn('biomass', validated)
    
    def test_validate_invalid_observations(self):
        """测试无效观测数据验证"""
        observations = {
            'lai': -1.0,  # 无效：小于最小值
            'biomass': 100000.0,  # 无效：大于最大值
            'valid_var': 5.0  # 有效
        }
        
        validated = self.controller.validate_observations(observations)
        
        self.assertEqual(len(validated), 1)
        self.assertIn('valid_var', validated)
        self.assertNotIn('lai', validated)
        self.assertNotIn('biomass', validated)


class TestUncertaintyManager(unittest.TestCase):
    """测试不确定性管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'observation_errors': {
                'lai': 0.5,
                'biomass': 100.0
            }
        }
        self.manager = UncertaintyManager(self.config)
    
    def test_estimate_observation_uncertainty(self):
        """测试观测不确定性估算"""
        observations = {
            'lai': 2.5,
            'biomass': 1000.0,
            'unknown_var': 10.0
        }
        
        uncertainties = self.manager.estimate_observation_uncertainty(observations)
        
        self.assertIn('lai_error', uncertainties)
        self.assertIn('biomass_error', uncertainties)
        self.assertIn('unknown_var_error', uncertainties)
        self.assertEqual(uncertainties['lai_error'], 0.5)
        self.assertEqual(uncertainties['biomass_error'], 100.0)
        self.assertEqual(uncertainties['unknown_var_error'], 1.0)  # 10% of 10.0


class TestAssimilationManager(unittest.TestCase):
    """测试数据同化管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'enable_assimilation': True,
            'assimilation_frequency': 1,
            'observation_dir': self.temp_dir,
            'quality_thresholds': {
                'lai': {'min': 0, 'max': 15}
            }
        }
        self.manager = AssimilationManager(self.config)
        
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_should_assimilate(self):
        """测试同化频率控制"""
        # 测试不同日期
        test_date1 = date(2013, 1, 1)  # 第1天
        test_date2 = date(2013, 1, 2)  # 第2天
        
        # 频率为1时，每天都应该同化
        self.assertTrue(self.manager._should_assimilate(test_date1))
        self.assertTrue(self.manager._should_assimilate(test_date2))
        
        # 修改频率为2
        self.manager.assimilation_frequency = 2
        self.assertFalse(self.manager._should_assimilate(test_date1))
        self.assertTrue(self.manager._should_assimilate(test_date2))
    
    def test_simple_assimilation_algorithm(self):
        """测试简单同化算法"""
        states = {
            'lai': 2.0,
            'biomass': 1000.0
        }
        
        observations = {
            'lai': 2.5,
            'lai_weight': 0.6,
            'biomass': 1200.0,
            'biomass_weight': 0.4
        }
        
        assimilated = self.manager._apply_simple_assimilation(states, observations)
        
        # 检查同化结果
        expected_lai = (1 - 0.6) * 2.0 + 0.6 * 2.5  # 2.3
        expected_biomass = (1 - 0.4) * 1000.0 + 0.4 * 1200.0  # 1080.0
        
        self.assertAlmostEqual(assimilated['lai'], expected_lai, places=5)
        self.assertAlmostEqual(assimilated['biomass'], expected_biomass, places=5)


if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 运行测试
    unittest.main()
