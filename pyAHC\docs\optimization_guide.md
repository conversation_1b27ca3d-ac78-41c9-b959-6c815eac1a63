# 日循环模拟优化指南

本指南介绍如何使用pyAHC的优化功能来提高模拟性能和稳定性，特别适用于长期模拟（数月到数年）。

## 概述

优化系统提供以下核心功能：

1. **内存管理**：智能垃圾回收和内存限制控制
2. **检查点恢复**：模拟中断后可从检查点恢复
3. **性能监控**：详细的执行时间和内存使用统计
4. **批量模拟**：参数扫描和并行处理
5. **状态缓存**：减少重复的数据加载操作

## 快速开始

### 基础优化模拟

```python
from datetime import date
from pyahc.db.hdf5 import run_optimized_project_simulation

# 配置优化参数
config = {
    # 基础配置
    'hdf5_path': 'optimized_simulation.h5',
    'project_name': 'corn_optimized_2023',
    'start_date': date(2023, 5, 1),
    'end_date': date(2023, 9, 30),
    'field_id': 'field_001',
    'crop_type': 'corn',
    'latitude': 45.75,
    'longitude': -93.22,
    'altitude': 300.0,
    
    # 启用优化
    'use_optimization': True,
    
    # 优化配置
    'optimization': {
        'memory_limit_mb': 2048,
        'gc_frequency': 10,
        'checkpoint_frequency': 50,
        'enable_parallel': False,
        'checkpoint_dir': './checkpoints'
    }
}

# 运行优化模拟
manager = run_optimized_project_simulation(config)
print("优化模拟完成！")
```

### 长期模拟配置

对于1000+天的长期模拟，推荐使用更保守的配置：

```python
# 长期模拟配置
long_term_config = {
    'hdf5_path': 'long_term_simulation.h5',
    'project_name': 'corn_3years_2020_2022',
    'start_date': date(2020, 1, 1),
    'end_date': date(2022, 12, 31),
    'field_id': 'field_longterm',
    'crop_type': 'corn',
    'latitude': 40.0,
    'longitude': -95.0,
    'altitude': 250.0,
    
    'use_optimization': True,
    'optimization': {
        'memory_limit_mb': 1536,  # 较低的内存限制
        'gc_frequency': 5,        # 更频繁的垃圾回收
        'checkpoint_frequency': 30,  # 更频繁的检查点
        'enable_parallel': False,    # 关闭并行以保证稳定性
        'checkpoint_dir': './checkpoints_longterm'
    }
}

manager = run_optimized_project_simulation(long_term_config)
```

## 批量模拟和参数扫描

### 基础参数扫描

```python
from pyahc.db.hdf5 import run_batch_simulation

# 基础配置
base_config = {
    'hdf5_path': 'batch_simulation.h5',
    'project_name': 'corn_parameter_sweep',
    'start_date': date(2023, 5, 1),
    'end_date': date(2023, 8, 31),
    'field_id': 'field_batch',
    'crop_type': 'corn',
    'latitude': 42.0,
    'longitude': -90.0,
    'altitude': 200.0,
    
    'use_optimization': True,
    'batch': {
        'enable_parallel_batch': True,
        'max_batch_workers': 4
    }
}

# 定义参数范围
parameter_ranges = {
    'irrigation_efficiency': [0.7, 0.8, 0.9, 1.0],
    'fertilizer_rate': [100, 150, 200, 250],  # kg/ha
    'planting_density': [70000, 80000, 90000]  # plants/ha
}

# 运行批量模拟
results = run_batch_simulation(base_config, parameter_ranges)

# 分析结果
successful_runs = [r for r in results if r.get('success', False)]
print(f"成功运行: {len(successful_runs)}/{len(results)}")

# 找到最佳参数组合
best_result = max(successful_runs, 
                 key=lambda x: x.get('statistics', {}).get('yield', 0))
print(f"最佳参数: {best_result['parameters']}")
print(f"最高产量: {best_result['statistics']['yield']}")
```

### 高级批量配置

```python
# 高性能批量配置
advanced_batch_config = base_config.copy()
advanced_batch_config.update({
    'optimization': {
        'memory_limit_mb': 3072,
        'gc_frequency': 15,
        'checkpoint_frequency': 100,
        'enable_parallel': True,
        'max_workers': 4
    },
    'batch': {
        'enable_parallel_batch': True,
        'max_batch_workers': 8,
        'batch_size': 10,
        'retry_failed': True,
        'max_retries': 2
    }
})

results = run_batch_simulation(advanced_batch_config, parameter_ranges)
```

## 性能监控和分析

### 查看性能报告

优化模拟会自动生成性能报告：

```python
# 运行模拟后，会生成性能报告文件
# 例如：corn_optimized_2023_performance_report.txt

# 报告内容包括：
# - 总模拟天数
# - 总执行时间
# - 平均每天执行时间
# - 模拟速度（天/小时）
# - 内存使用统计
# - 每日性能数据
```

### 自定义性能监控

```python
from pyahc.db.hdf5.optimization import OptimizedSimulationEngine, PerformanceMonitor

# 创建自定义性能监控
monitor = PerformanceMonitor()
monitor.start_monitoring()

# 在模拟过程中记录性能
for day in simulation_days:
    start_time = time.time()
    # ... 执行模拟 ...
    duration = time.time() - start_time
    memory_usage = get_memory_usage()
    
    monitor.record_day_performance(day, duration, memory_usage)

# 获取统计信息
stats = monitor.get_stats()
print(f"平均每天执行时间: {stats['avg_day_duration']:.3f}秒")
print(f"模拟速度: {stats['days_per_hour']:.1f}天/小时")
print(f"峰值内存使用: {stats['max_memory_mb']:.1f}MB")
```

## 检查点和恢复

### 自动检查点

优化引擎会自动保存检查点：

```python
config = {
    # ... 其他配置 ...
    'optimization': {
        'checkpoint_frequency': 50,  # 每50天保存检查点
        'checkpoint_dir': './checkpoints'
    }
}

# 如果模拟中断，重新运行时会自动从最新检查点恢复
manager = run_optimized_project_simulation(config)
```

### 手动检查点管理

```python
from pyahc.db.hdf5.optimization import CheckpointManager

# 创建检查点管理器
checkpoint_config = {'checkpoint_dir': './my_checkpoints'}
checkpoint_manager = CheckpointManager(checkpoint_config)

# 保存检查点
checkpoint_manager.save_checkpoint('my_project', current_date, performance_stats)

# 查找恢复点
resume_date = checkpoint_manager.find_resume_point('my_project', start_date, end_date)
if resume_date:
    print(f"可以从 {resume_date} 恢复模拟")
```

## 配置文件使用

### YAML配置文件

```python
import yaml
from pyahc.db.hdf5 import run_optimized_project_simulation

# 加载YAML配置
with open('optimized_config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 转换日期格式
from datetime import datetime
config['start_date'] = datetime.strptime(config['simulation']['start_date'], '%Y-%m-%d').date()
config['end_date'] = datetime.strptime(config['simulation']['end_date'], '%Y-%m-%d').date()

# 运行模拟
manager = run_optimized_project_simulation(config)
```

### 预定义配置模板

系统提供了几个预定义的配置模板：

1. `optimized_config.yaml` - 通用优化配置
2. `long_term_simulation_config.yaml` - 长期模拟配置
3. `batch_simulation_config.yaml` - 批量模拟配置

## 最佳实践

### 内存管理

1. **设置合适的内存限制**：根据系统内存设置`memory_limit_mb`
2. **调整垃圾回收频率**：长期模拟使用较小的`gc_frequency`
3. **监控内存使用**：定期检查性能报告中的内存统计

### 检查点策略

1. **长期模拟**：使用较小的`checkpoint_frequency`（如30天）
2. **短期模拟**：可以使用较大的`checkpoint_frequency`（如100天）
3. **批量模拟**：通常不需要频繁的检查点

### 并行处理

1. **单个模拟**：长期模拟建议关闭并行（`enable_parallel: false`）
2. **批量模拟**：可以启用批量并行（`enable_parallel_batch: true`）
3. **资源限制**：根据CPU核心数设置`max_workers`

### 错误处理

1. **长期模拟**：设置`stop_on_error: false`以跳过单日错误
2. **批量模拟**：启用`retry_failed: true`重试失败的模拟
3. **调试模式**：设置`stop_on_error: true`便于调试

## 故障排除

### 常见问题

1. **内存不足**：减少`memory_limit_mb`或增加`gc_frequency`
2. **模拟速度慢**：检查并行设置和系统资源
3. **检查点恢复失败**：检查检查点文件完整性
4. **批量模拟失败**：减少`max_batch_workers`或检查参数范围

### 性能调优

1. **监控系统资源**：使用系统监控工具观察CPU和内存使用
2. **调整配置参数**：根据性能报告调整优化参数
3. **测试不同配置**：在小规模数据上测试不同的配置组合
