"""优化功能示例

演示如何使用pyAHC的优化功能进行高性能模拟。
包括基础优化、长期模拟、批量处理等示例。
"""

import logging
import time
import yaml
from datetime import date, timedelta
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_optimized_config():
    """创建优化配置示例"""
    return {
        # 基础配置
        'hdf5_path': 'optimized_demo.h5',
        'project_name': 'corn_optimized_demo',
        'start_date': date(2023, 5, 1),
        'end_date': date(2023, 5, 31),  # 1个月示例
        'field_id': 'demo_field',
        'crop_type': 'corn',
        'variety': 'standard',
        'planting_date': date(2023, 5, 1),
        'latitude': 45.75,
        'longitude': -93.22,
        'altitude': 300.0,
        
        # 启用优化
        'use_optimization': True,
        
        # 优化配置
        'optimization': {
            'memory_limit_mb': 1024,
            'gc_frequency': 5,
            'checkpoint_frequency': 10,
            'enable_parallel': False,
            'checkpoint_dir': './demo_checkpoints'
        }
    }


def create_long_term_config():
    """创建长期模拟配置示例"""
    return {
        # 基础配置
        'hdf5_path': 'long_term_demo.h5',
        'project_name': 'corn_long_term_demo',
        'start_date': date(2023, 1, 1),
        'end_date': date(2023, 12, 31),  # 1年示例
        'field_id': 'longterm_field',
        'crop_type': 'corn',
        'variety': 'drought_resistant',
        'planting_date': date(2023, 5, 1),
        'latitude': 40.0,
        'longitude': -95.0,
        'altitude': 250.0,
        
        # 启用优化
        'use_optimization': True,
        
        # 长期模拟优化配置
        'optimization': {
            'memory_limit_mb': 512,   # 更严格的内存限制
            'gc_frequency': 3,        # 更频繁的垃圾回收
            'checkpoint_frequency': 15,  # 更频繁的检查点
            'enable_parallel': False,    # 关闭并行保证稳定性
            'checkpoint_dir': './longterm_checkpoints'
        }
    }


def create_batch_config():
    """创建批量模拟配置示例"""
    return {
        # 基础配置
        'hdf5_path': 'batch_demo.h5',
        'project_name': 'corn_batch_demo',
        'start_date': date(2023, 5, 1),
        'end_date': date(2023, 7, 31),  # 3个月示例
        'field_id': 'batch_field',
        'crop_type': 'corn',
        'variety': 'standard',
        'planting_date': date(2023, 5, 1),
        'latitude': 42.0,
        'longitude': -90.0,
        'altitude': 200.0,
        
        # 启用优化
        'use_optimization': True,
        
        # 批量优化配置
        'optimization': {
            'memory_limit_mb': 2048,
            'gc_frequency': 10,
            'checkpoint_frequency': 50,
            'enable_parallel': True,
            'max_workers': 2
        },
        
        # 批量配置
        'batch': {
            'enable_parallel_batch': True,
            'max_batch_workers': 4
        }
    }


def run_basic_optimization_demo():
    """运行基础优化示例"""
    logger.info("=== 基础优化模拟示例 ===")
    
    try:
        from pyahc.db.hdf5 import run_optimized_project_simulation
        
        # 创建配置
        config = create_optimized_config()
        
        logger.info(f"项目: {config['project_name']}")
        logger.info(f"模拟期间: {config['start_date']} 到 {config['end_date']}")
        logger.info(f"优化设置: 内存限制={config['optimization']['memory_limit_mb']}MB")
        
        # 记录开始时间
        start_time = time.time()
        
        # 运行优化模拟
        manager = run_optimized_project_simulation(config)
        
        # 计算执行时间
        duration = time.time() - start_time
        
        logger.info(f"基础优化模拟完成，耗时: {duration:.2f}秒")
        
        # 显示结果摘要
        show_simulation_summary(manager, config['project_name'])
        
    except Exception as e:
        logger.error(f"基础优化模拟失败: {e}")
        raise


def run_long_term_simulation_demo():
    """运行长期模拟示例"""
    logger.info("=== 长期模拟示例 ===")
    
    try:
        from pyahc.db.hdf5 import run_optimized_project_simulation
        
        # 创建长期配置
        config = create_long_term_config()
        
        # 为演示目的，缩短模拟期间
        config['end_date'] = config['start_date'] + timedelta(days=30)  # 30天演示
        
        logger.info(f"项目: {config['project_name']}")
        logger.info(f"模拟期间: {config['start_date']} 到 {config['end_date']}")
        logger.info("长期模拟配置: 严格内存管理，频繁检查点")
        
        # 记录开始时间
        start_time = time.time()
        
        # 运行长期模拟
        manager = run_optimized_project_simulation(config)
        
        # 计算执行时间
        duration = time.time() - start_time
        
        logger.info(f"长期模拟完成，耗时: {duration:.2f}秒")
        
        # 显示结果摘要
        show_simulation_summary(manager, config['project_name'])
        
    except Exception as e:
        logger.error(f"长期模拟失败: {e}")
        raise


def run_batch_simulation_demo():
    """运行批量模拟示例"""
    logger.info("=== 批量模拟示例 ===")
    
    try:
        from pyahc.db.hdf5 import run_batch_simulation
        
        # 创建批量配置
        base_config = create_batch_config()
        
        # 为演示目的，缩短模拟期间
        base_config['end_date'] = base_config['start_date'] + timedelta(days=14)  # 2周演示
        
        # 定义参数范围（简化的示例）
        parameter_ranges = {
            'irrigation_efficiency': [0.8, 0.9, 1.0],
            'fertilizer_rate': [150, 200, 250]  # kg/ha
        }
        
        logger.info(f"基础项目: {base_config['project_name']}")
        logger.info(f"参数范围: {parameter_ranges}")
        
        # 计算总组合数
        total_combinations = 1
        for param_values in parameter_ranges.values():
            total_combinations *= len(param_values)
        logger.info(f"总参数组合数: {total_combinations}")
        
        # 记录开始时间
        start_time = time.time()
        
        # 运行批量模拟
        results = run_batch_simulation(base_config, parameter_ranges)
        
        # 计算执行时间
        duration = time.time() - start_time
        
        logger.info(f"批量模拟完成，耗时: {duration:.2f}秒")
        
        # 分析结果
        analyze_batch_results(results)
        
    except Exception as e:
        logger.error(f"批量模拟失败: {e}")
        raise


def run_yaml_config_demo():
    """使用YAML配置文件运行模拟"""
    logger.info("=== YAML配置文件示例 ===")
    
    try:
        # 创建示例YAML配置文件
        yaml_config = {
            'project': {
                'name': 'yaml_demo',
                'description': 'YAML configuration demo'
            },
            'storage': {
                'hdf5_path': 'yaml_demo.h5'
            },
            'simulation': {
                'start_date': '2023-05-01',
                'end_date': '2023-05-15'
            },
            'location': {
                'field_id': 'yaml_field',
                'latitude': 45.0,
                'longitude': -93.0,
                'altitude': 300.0
            },
            'crop': {
                'type': 'corn',
                'variety': 'standard',
                'planting_date': '2023-05-01'
            },
            'optimization': {
                'memory_limit_mb': 1024,
                'gc_frequency': 5,
                'checkpoint_frequency': 10,
                'enable_parallel': False,
                'checkpoint_dir': './yaml_checkpoints'
            }
        }
        
        # 保存YAML文件
        yaml_file = Path('demo_config.yaml')
        with open(yaml_file, 'w') as f:
            yaml.dump(yaml_config, f, default_flow_style=False)
        
        logger.info(f"创建YAML配置文件: {yaml_file}")
        
        # 加载并转换配置
        config = load_yaml_config(yaml_file)
        
        # 运行模拟
        from pyahc.db.hdf5 import run_optimized_project_simulation
        manager = run_optimized_project_simulation(config)
        
        logger.info("YAML配置模拟完成")
        
        # 清理临时文件
        yaml_file.unlink()
        
    except Exception as e:
        logger.error(f"YAML配置模拟失败: {e}")
        raise


def load_yaml_config(yaml_file):
    """加载并转换YAML配置"""
    with open(yaml_file, 'r') as f:
        yaml_config = yaml.safe_load(f)
    
    # 转换为标准配置格式
    from datetime import datetime
    
    config = {
        'hdf5_path': yaml_config['storage']['hdf5_path'],
        'project_name': yaml_config['project']['name'],
        'start_date': datetime.strptime(yaml_config['simulation']['start_date'], '%Y-%m-%d').date(),
        'end_date': datetime.strptime(yaml_config['simulation']['end_date'], '%Y-%m-%d').date(),
        'field_id': yaml_config['location']['field_id'],
        'crop_type': yaml_config['crop']['type'],
        'variety': yaml_config['crop']['variety'],
        'planting_date': datetime.strptime(yaml_config['crop']['planting_date'], '%Y-%m-%d').date(),
        'latitude': yaml_config['location']['latitude'],
        'longitude': yaml_config['location']['longitude'],
        'altitude': yaml_config['location']['altitude'],
        'use_optimization': True,
        'optimization': yaml_config['optimization']
    }
    
    return config


def show_simulation_summary(manager, project_name):
    """显示模拟结果摘要"""
    try:
        with manager:
            # 获取项目元数据
            metadata = manager.get_project_metadata(project_name)
            logger.info(f"模拟天数: {metadata.get('simulation_period', {}).get('total_days', 'N/A')}")
            
            # 获取可用日期
            available_dates = manager.get_available_dates(project_name)
            logger.info(f"可用数据日期: {len(available_dates)} 天")
            
            if available_dates:
                # 显示前几天的状态示例
                for i, date_obj in enumerate(sorted(available_dates)[:3]):
                    daily_data = manager.load_daily_data(project_name, date_obj)
                    states = daily_data.get('states', {})
                    
                    lai = states.get('lai', 'N/A')
                    biomass = states.get('biomass', 'N/A')
                    
                    logger.info(f"  {date_obj}: LAI={lai}, 生物量={biomass}")
    
    except Exception as e:
        logger.error(f"显示摘要失败: {e}")


def analyze_batch_results(results):
    """分析批量模拟结果"""
    try:
        successful_runs = [r for r in results if r.get('success', False)]
        failed_runs = [r for r in results if not r.get('success', False)]
        
        logger.info(f"成功运行: {len(successful_runs)}")
        logger.info(f"失败运行: {len(failed_runs)}")
        
        if successful_runs:
            # 计算平均执行时间
            avg_duration = sum(r['duration'] for r in successful_runs) / len(successful_runs)
            logger.info(f"平均执行时间: {avg_duration:.2f}秒")
            
            # 显示参数组合示例
            logger.info("参数组合示例:")
            for i, result in enumerate(successful_runs[:3]):
                params = result['parameters']
                duration = result['duration']
                logger.info(f"  组合{i+1}: {params}, 耗时: {duration:.2f}秒")
        
        if failed_runs:
            logger.warning("失败的运行:")
            for i, result in enumerate(failed_runs[:3]):
                params = result['parameters']
                error = result.get('error', 'Unknown error')
                logger.warning(f"  失败{i+1}: {params}, 错误: {error}")
    
    except Exception as e:
        logger.error(f"分析批量结果失败: {e}")


def main():
    """主函数，运行所有示例"""
    logger.info("开始优化功能演示")
    
    try:
        # 1. 基础优化示例
        run_basic_optimization_demo()
        
        # 2. 长期模拟示例
        run_long_term_simulation_demo()
        
        # 3. 批量模拟示例
        run_batch_simulation_demo()
        
        # 4. YAML配置示例
        run_yaml_config_demo()
        
        logger.info("所有优化功能演示完成！")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise


if __name__ == '__main__':
    main()
