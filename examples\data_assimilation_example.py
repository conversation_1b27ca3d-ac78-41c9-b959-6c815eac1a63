"""数据同化接口使用示例

演示如何使用pyAHC的数据同化功能：
1. 配置数据同化参数
2. 运行带数据同化的项目模拟
3. 分析同化结果
"""

import logging
from datetime import date, timedelta
from pathlib import Path

from pyahc.db.hdf5 import (
    run_project_simulation,
    ProjectHDF5Manager,
    AssimilationManager
)


def main():
    """数据同化示例主函数"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 项目配置
    config = {
        # 基本项目配置
        'hdf5_path': 'assimilation_example.h5',
        'project_name': 'corn_assimilation_test',
        'start_date': date(2013, 5, 1),
        'end_date': date(2013, 5, 10),
        'field_id': 'field_001',
        'crop_type': 'corn',
        'latitude': 45.75,
        'longitude': None,
        'altitude': 1039.3,
        
        # 数据同化配置
        'enable_assimilation': True,
        'assimilation_frequency': 2,  # 每2天同化一次
        'observation_window': 1,
        'observation_dir': './sample_observations',
        
        # 质量控制参数
        'quality_thresholds': {
            'lai': {'min': 0, 'max': 15},
            'biomass': {'min': 0, 'max': 50000},
            'soil_moisture': {'min': 0, 'max': 1},
            'root_depth': {'min': 0, 'max': 500}
        },
        
        # 观测误差参数
        'observation_errors': {
            'lai': 0.1,
            'biomass': 50.0,
            'soil_moisture': 0.02,
            'root_depth': 5.0
        },
        
        # 其他配置
        'stop_on_error': False,
        'enable_outlier_detection': True,
        'outlier_threshold': 3.0
    }
    
    print("=== pyAHC 数据同化示例 ===")
    print(f"项目名称: {config['project_name']}")
    print(f"模拟期间: {config['start_date']} 到 {config['end_date']}")
    print(f"同化频率: 每{config['assimilation_frequency']}天")
    print(f"观测数据目录: {config['observation_dir']}")
    print()
    
    # 检查观测数据目录
    obs_dir = Path(config['observation_dir'])
    if not obs_dir.exists():
        print(f"警告: 观测数据目录 {obs_dir} 不存在")
        print("请确保观测数据文件存在于指定目录中")
        return
    
    # 列出可用的观测数据文件
    obs_files = list(obs_dir.glob('*'))
    print(f"找到 {len(obs_files)} 个观测数据文件:")
    for file in obs_files:
        print(f"  - {file.name}")
    print()
    
    try:
        # 运行带数据同化的项目模拟
        print("开始运行项目模拟...")
        manager = run_project_simulation(config)
        
        print("模拟完成！")
        print()
        
        # 分析同化结果
        analyze_assimilation_results(manager, config)
        
    except Exception as e:
        print(f"模拟失败: {e}")
        logging.error(f"Simulation failed: {e}", exc_info=True)


def analyze_assimilation_results(manager: ProjectHDF5Manager, config: dict):
    """分析数据同化结果"""
    
    print("=== 数据同化结果分析 ===")
    
    project_name = config['project_name']
    start_date = config['start_date']
    end_date = config['end_date']
    
    try:
        with manager:
            # 检查项目是否存在
            if project_name not in manager._file:
                print(f"项目 {project_name} 不存在")
                return
            
            project_group = manager._file[project_name]
            
            # 统计同化执行情况
            assimilation_days = []
            total_days = (end_date - start_date).days + 1
            
            current_date = start_date
            while current_date <= end_date:
                date_str = f"day_{current_date.strftime('%m-%d')}"
                
                if date_str in project_group:
                    day_group = project_group[date_str]
                    if 'assimilation' in day_group:
                        assimilation_days.append(current_date)
                
                current_date += timedelta(days=1)
            
            print(f"总模拟天数: {total_days}")
            print(f"执行同化天数: {len(assimilation_days)}")
            print(f"同化执行率: {len(assimilation_days)/total_days*100:.1f}%")
            print()
            
            if assimilation_days:
                print("同化执行日期:")
                for assim_date in assimilation_days:
                    print(f"  - {assim_date}")
                print()
                
                # 分析最后一次同化结果
                last_assim_date = assimilation_days[-1]
                analyze_single_assimilation(manager, project_name, last_assim_date)
            
    except Exception as e:
        print(f"结果分析失败: {e}")
        logging.error(f"Analysis failed: {e}", exc_info=True)


def analyze_single_assimilation(manager: ProjectHDF5Manager, 
                              project_name: str, 
                              assim_date: date):
    """分析单次同化结果"""
    
    print(f"=== {assim_date} 同化结果详情 ===")
    
    try:
        date_str = f"day_{assim_date.strftime('%m-%d')}"
        project_group = manager._file[project_name]
        day_group = project_group[date_str]
        assim_group = day_group['assimilation']
        
        # 显示元数据
        print("同化元数据:")
        for attr_name in assim_group.attrs:
            print(f"  {attr_name}: {assim_group.attrs[attr_name]}")
        print()
        
        # 显示观测数据
        if 'observations' in assim_group:
            obs_group = assim_group['observations']
            print("观测数据:")
            for attr_name in obs_group.attrs:
                print(f"  {attr_name}: {obs_group.attrs[attr_name]}")
            print()
        
        # 显示统计信息
        if 'statistics' in assim_group:
            stats_group = assim_group['statistics']
            print("同化统计:")
            for attr_name in stats_group.attrs:
                print(f"  {attr_name}: {stats_group.attrs[attr_name]:.4f}")
            print()
        
    except Exception as e:
        print(f"单次同化分析失败: {e}")


if __name__ == '__main__':
    main()
