#!/usr/bin/env python3
"""工作流程验证脚本

验证HDF5工作流程集成的基本功能，包括：
1. 项目初始化
2. 配置验证
3. 元数据生成
4. 模块导入
"""

import sys
import os
from datetime import date
from pathlib import Path
import tempfile
import shutil

# 添加pyAHC到Python路径
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试核心组件导入
        from pyahc.db.hdf5 import (
            ProjectHDF5Manager,
            StateExtractor,
            StateInjector,
            initialize_project_simulation,
            daily_simulation_step,
            generate_project_summary,
            run_project_simulation
        )
        print("✓ 所有工作流程函数导入成功")
        
        # 测试异常类导入
        from pyahc.db.hdf5 import (
            HDF5Error,
            ProjectNotFoundError,
            DataNotFoundError,
            InvalidDataFormatError
        )
        print("✓ 所有异常类导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_configuration_validation():
    """测试配置验证"""
    print("\n=== 测试配置验证 ===")
    
    try:
        from pyahc.db.hdf5.workflow import _validate_project_config
        
        # 测试有效配置
        valid_config = {
            'hdf5_path': 'test.h5',
            'project_name': 'test_project',
            'start_date': '2023-05-01',
            'end_date': '2023-05-05'
        }
        
        validated = _validate_project_config(valid_config)
        assert isinstance(validated['start_date'], date)
        assert isinstance(validated['end_date'], date)
        print("✓ 配置验证功能正常")
        
        # 测试无效配置
        try:
            invalid_config = {
                'hdf5_path': 'test.h5',
                'project_name': 'test_project'
                # 缺少必需的日期字段
            }
            _validate_project_config(invalid_config)
            print("✗ 配置验证应该失败但没有失败")
            return False
        except ValueError:
            print("✓ 无效配置正确被拒绝")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置验证测试失败: {e}")
        return False


def test_project_initialization():
    """测试项目初始化"""
    print("\n=== 测试项目初始化 ===")
    
    test_dir = None
    try:
        from pyahc.db.hdf5.workflow import initialize_project_simulation
        
        # 创建临时目录
        test_dir = tempfile.mkdtemp()
        hdf5_path = Path(test_dir) / "test_validation.h5"
        
        config = {
            'hdf5_path': str(hdf5_path),
            'project_name': 'validation_test',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 3),
            'field_id': 'test_field',
            'crop_type': 'corn'
        }
        
        # 初始化项目
        manager = initialize_project_simulation(config)

        # 验证项目创建
        assert manager.project_exists('validation_test')
        print("✓ 项目初始化成功")

        # 验证元数据
        metadata = manager.get_project_metadata('validation_test')
        assert 'model_info' in metadata
        assert 'simulation_period' in metadata
        print("✓ 项目元数据生成正确")

        # 确保HDF5文件关闭
        if hasattr(manager, '_file') and manager._file is not None:
            manager._file.close()

        return True
        
    except Exception as e:
        print(f"✗ 项目初始化测试失败: {e}")
        return False
        
    finally:
        # 清理临时目录
        if test_dir and os.path.exists(test_dir):
            shutil.rmtree(test_dir)


def test_model_creation():
    """测试模型创建"""
    print("\n=== 测试模型创建 ===")
    
    try:
        from pyahc.db.hdf5.workflow import _create_base_model
        
        config = {
            'project_name': 'test_model',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 3),
            'latitude': 40.0,
            'longitude': -100.0,
            'altitude': 100.0
        }
        
        # 创建基础模型
        model = _create_base_model(config)
        
        # 验证模型结构
        assert hasattr(model, 'generalsettings')
        assert hasattr(model, 'crop')
        assert hasattr(model, 'soilprofile')
        print("✓ 基础模型创建成功")
        
        # 验证日期设置
        assert model.generalsettings.tstart == config['start_date']
        assert model.generalsettings.tend == config['end_date']
        print("✓ 模型日期设置正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建测试失败: {e}")
        return False


def main():
    """主验证函数"""
    print("HDF5工作流程集成验证")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration_validation,
        test_project_initialization,
        test_model_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 验证结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有验证测试通过！工作流程集成实现成功。")
        return 0
    else:
        print(f"✗ {total - passed} 个测试失败。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
