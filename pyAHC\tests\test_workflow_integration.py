"""工作流程集成测试

测试HDF5工作流程集成的完整功能，包括项目初始化、日循环模拟、项目汇总等。
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import date, datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import logging

# 设置测试环境
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from pyahc.db.hdf5.workflow import (
    initialize_project_simulation,
    daily_simulation_step,
    generate_project_summary,
    run_project_simulation,
    _validate_project_config,
    _generate_project_metadata,
    _create_base_model
)
from pyahc.db.hdf5.manager import ProjectHDF5Manager
from pyahc.db.hdf5.exceptions import HDF5Error


class PickleableTestModel:
    """可序列化的测试模型类，用于测试HDF5存储功能"""
    def __init__(self):
        self.generalsettings = PickleableTestSettings()
        self.name = "TestModel"
        self.version = "1.0"
        self.crop = None
        self.soilprofile = None
        self.meteorology = None

    def model_copy(self, deep=True):
        """创建模型副本"""
        new_model = PickleableTestModel()
        new_model.generalsettings = PickleableTestSettings()
        new_model.generalsettings.tstart = self.generalsettings.tstart
        new_model.generalsettings.tend = self.generalsettings.tend
        return new_model

    def run(self):
        """运行模型并返回结果"""
        return PickleableTestResult()


class PickleableTestSettings:
    """可序列化的测试设置类"""
    def __init__(self):
        self.tstart = date(2023, 5, 1)
        self.tend = date(2023, 5, 1)


class PickleableTestResult:
    """可序列化的测试结果类"""
    def __init__(self):
        self.csv = None
        self.ascii = {}
        self.output = {'csv': None, 'ascii': {}}
        self.log = "Test model run completed"
        self.warning = []


class TestWorkflowIntegration(unittest.TestCase):
    """工作流程集成测试类"""

    def setUp(self):
        """测试设置"""
        self.test_dir = tempfile.mkdtemp()
        self.hdf5_path = Path(self.test_dir) / "test_project.h5"
        self.logger = logging.getLogger(__name__)

        # 基本配置
        self.base_config = {
            'hdf5_path': str(self.hdf5_path),
            'project_name': 'test_corn_2023',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 5),
            'field_id': 'field_001',
            'crop_type': 'corn',
            'latitude': 40.0,
            'longitude': -100.0,
            'altitude': 500.0
        }
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_validate_project_config_valid(self):
        """测试有效配置验证"""
        config = self.base_config.copy()
        validated = _validate_project_config(config)
        
        self.assertIsInstance(validated['start_date'], date)
        self.assertIsInstance(validated['end_date'], date)
        self.assertEqual(validated['field_id'], 'field_001')
        self.assertEqual(validated['crop_type'], 'corn')
    
    def test_validate_project_config_missing_required(self):
        """测试缺少必需参数的配置验证"""
        config = self.base_config.copy()
        del config['project_name']
        
        with self.assertRaises(ValueError) as context:
            _validate_project_config(config)
        
        self.assertIn("Missing required configuration key: project_name", str(context.exception))
    
    def test_validate_project_config_invalid_dates(self):
        """测试无效日期配置验证"""
        config = self.base_config.copy()
        config['start_date'] = date(2023, 5, 10)
        config['end_date'] = date(2023, 5, 5)
        
        with self.assertRaises(ValueError) as context:
            _validate_project_config(config)
        
        self.assertIn("Start date must be before end date", str(context.exception))
    
    def test_validate_project_config_string_dates(self):
        """测试字符串日期格式转换"""
        config = self.base_config.copy()
        config['start_date'] = '2023-05-01'
        config['end_date'] = '2023-05-05'
        
        validated = _validate_project_config(config)
        
        self.assertIsInstance(validated['start_date'], date)
        self.assertIsInstance(validated['end_date'], date)
        self.assertEqual(validated['start_date'], date(2023, 5, 1))
        self.assertEqual(validated['end_date'], date(2023, 5, 5))
    
    def test_generate_project_metadata(self):
        """测试项目元数据生成"""
        config = _validate_project_config(self.base_config.copy())
        metadata = _generate_project_metadata(config)
        
        # 验证元数据结构
        self.assertIn('model_info', metadata)
        self.assertIn('simulation_period', metadata)
        self.assertIn('location_info', metadata)
        self.assertIn('crop_info', metadata)
        self.assertIn('configuration', metadata)
        
        # 验证具体内容
        self.assertEqual(metadata['simulation_period']['total_days'], 5)
        self.assertEqual(metadata['location_info']['field_id'], 'field_001')
        self.assertEqual(metadata['crop_info']['crop_type'], 'corn')
    
    def test_initialize_project_simulation(self):
        """测试项目初始化"""
        config = self.base_config.copy()
        
        manager = initialize_project_simulation(config)
        
        self.assertIsInstance(manager, ProjectHDF5Manager)
        self.assertTrue(manager.project_exists('test_corn_2023'))
        
        # 验证元数据
        metadata = manager.get_project_metadata('test_corn_2023')
        self.assertIn('model_info', metadata)
        self.assertIn('simulation_period', metadata)
    
    def test_initialize_project_simulation_existing_project(self):
        """测试初始化已存在的项目"""
        config = self.base_config.copy()
        
        # 第一次初始化
        manager1 = initialize_project_simulation(config)
        
        # 第二次初始化（应该跳过创建）
        with self.assertLogs(level='WARNING') as log:
            manager2 = initialize_project_simulation(config)
        
        self.assertIn("already exists", log.output[0])
        self.assertTrue(manager2.project_exists('test_corn_2023'))
    
    @patch('pyahc.db.hdf5.workflow.StateExtractor.extract_all_states')
    @patch('pyahc.db.hdf5.workflow.StateInjector.inject_all_states')
    def test_daily_simulation_step(self, mock_inject, mock_extract):
        """测试单日模拟步骤"""
        # 设置模拟对象
        test_model = PickleableTestModel()

        mock_extract.return_value = {
            'lai': 2.5,
            'biomass': 3000.0,
            'soil_moisture': np.array([0.25, 0.23])
        }

        mock_inject.return_value = test_model

        # 初始化项目
        manager = initialize_project_simulation(self.base_config)

        # 执行单日模拟
        current_date = date(2023, 5, 1)
        result = daily_simulation_step(manager, 'test_corn_2023', current_date, test_model)

        # 验证调用
        mock_extract.assert_called_once()

        # 验证数据保存
        with manager:
            daily_data = manager.load_daily_data('test_corn_2023', current_date)
            self.assertIn('states', daily_data)
    
    @patch('pyahc.db.hdf5.workflow._create_base_model')
    def test_generate_project_summary(self, mock_create_model):
        """测试项目汇总生成"""
        # 初始化项目
        manager = initialize_project_simulation(self.base_config)
        
        # 创建一些模拟数据
        test_states = {
            'lai': 2.5,
            'biomass': 3000.0,
            'soil_moisture': np.array([0.25, 0.23]),
            'root_depth': 50.0,
            'groundwater_level': -150.0
        }
        
        # 保存几天的数据
        test_model = PickleableTestModel()
        test_result = PickleableTestResult()

        for i in range(3):
            test_date = date(2023, 5, 1) + timedelta(days=i)
            with manager:
                manager.save_daily_data(
                    'test_corn_2023', test_date, test_model, test_result,
                    test_states, {}
                )
        
        # 生成汇总
        generate_project_summary(
            manager, 'test_corn_2023', 
            date(2023, 5, 1), date(2023, 5, 3)
        )
        
        # 验证汇总数据
        with manager:
            project_group = manager._file['test_corn_2023']
            self.assertIn('summary', project_group)
            
            summary_group = project_group['summary']
            self.assertIn('timeseries', summary_group)
            self.assertIn('statistics', summary_group)
            
            # 验证时间序列数据
            timeseries_group = summary_group['timeseries']
            self.assertIn('lai_timeseries', timeseries_group)
            self.assertIn('biomass_timeseries', timeseries_group)
            
            # 验证统计数据
            statistics_group = summary_group['statistics']
            self.assertIn('lai_mean', statistics_group.attrs)
            self.assertIn('biomass_max', statistics_group.attrs)
    
    def test_create_base_model(self):
        """测试基础模型创建"""
        config = self.base_config.copy()
        
        try:
            model = _create_base_model(config)
            
            # 验证模型结构
            self.assertIsNotNone(model)
            self.assertIsNotNone(model.generalsettings)
            self.assertEqual(model.generalsettings.tstart, config['start_date'])
            self.assertEqual(model.generalsettings.tend, config['end_date'])
            
        except ImportError:
            # 如果无法导入模型组件，跳过测试
            self.skipTest("Model components not available for testing")
    
    @patch('pyahc.db.hdf5.workflow.daily_simulation_step')
    @patch('pyahc.db.hdf5.workflow._create_base_model')
    def test_run_project_simulation_complete(self, mock_create_model, mock_daily_step):
        """测试完整项目模拟运行"""
        # 设置模拟对象
        test_model = PickleableTestModel()
        mock_create_model.return_value = test_model

        test_result = PickleableTestResult()
        mock_daily_step.return_value = test_result
        
        config = self.base_config.copy()
        
        # 运行完整模拟
        manager = run_project_simulation(config)
        
        # 验证调用次数（5天）
        self.assertEqual(mock_daily_step.call_count, 5)
        
        # 验证项目存在
        self.assertTrue(manager.project_exists('test_corn_2023'))
        
        # 验证汇总数据生成
        with manager:
            project_group = manager._file['test_corn_2023']
            self.assertIn('summary', project_group)


if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    # 运行测试
    unittest.main()
