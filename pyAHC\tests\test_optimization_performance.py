"""性能优化测试套件

测试优化的模拟引擎、性能监控、检查点管理和批量模拟功能。
验证性能提升和系统稳定性。
"""

import unittest
import tempfile
import shutil
import time
import psutil
from pathlib import Path
from datetime import date, timedelta
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import logging

# 设置测试环境
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from pyahc.db.hdf5.optimization import (
    OptimizedSimulationEngine,
    PerformanceMonitor,
    CheckpointManager,
    BatchSimulationManager
)
from pyahc.db.hdf5.manager import ProjectHDF5Manager


class TestPerformanceMonitor(unittest.TestCase):
    """性能监控器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.monitor = PerformanceMonitor()
    
    def test_monitoring_lifecycle(self):
        """测试监控生命周期"""
        # 开始监控
        self.monitor.start_monitoring()
        self.assertTrue(self.monitor.is_monitoring)
        self.assertIsNotNone(self.monitor.start_time)
        
        # 记录性能数据
        test_date = date(2023, 5, 1)
        self.monitor.record_day_performance(test_date, 1.5, 512.0)
        
        # 获取统计信息
        stats = self.monitor.get_stats()
        self.assertEqual(stats['total_days'], 1)
        self.assertEqual(stats['avg_day_duration'], 1.5)
        self.assertEqual(stats['avg_memory_mb'], 512.0)
        
        # 停止监控
        self.monitor.stop_monitoring()
        self.assertFalse(self.monitor.is_monitoring)
    
    def test_performance_statistics(self):
        """测试性能统计计算"""
        self.monitor.start_monitoring()
        
        # 记录多天数据
        test_dates = [date(2023, 5, i) for i in range(1, 6)]
        durations = [1.0, 1.5, 2.0, 1.2, 1.8]
        memories = [500, 520, 480, 510, 530]
        
        for test_date, duration, memory in zip(test_dates, durations, memories):
            self.monitor.record_day_performance(test_date, duration, memory)
        
        stats = self.monitor.get_stats()
        
        # 验证统计信息
        self.assertEqual(stats['total_days'], 5)
        self.assertAlmostEqual(stats['avg_day_duration'], np.mean(durations), places=3)
        self.assertAlmostEqual(stats['max_day_duration'], max(durations), places=3)
        self.assertAlmostEqual(stats['min_day_duration'], min(durations), places=3)
        self.assertAlmostEqual(stats['avg_memory_mb'], np.mean(memories), places=1)
        self.assertAlmostEqual(stats['max_memory_mb'], max(memories), places=1)
        
        # 验证趋势数据
        trend = self.monitor.get_performance_trend()
        self.assertEqual(len(trend['dates']), 5)
        self.assertEqual(len(trend['durations']), 5)
        self.assertEqual(len(trend['memory_usage']), 5)


class TestCheckpointManager(unittest.TestCase):
    """检查点管理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'checkpoint_dir': self.temp_dir
        }
        self.manager = CheckpointManager(self.config)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_checkpoint_save_and_load(self):
        """测试检查点保存和加载"""
        project_name = "test_project"
        current_date = date(2023, 5, 15)
        stats = {'total_days': 10, 'avg_duration': 1.5}
        
        # 保存检查点
        self.manager.save_checkpoint(project_name, current_date, stats)
        
        # 验证文件存在
        checkpoint_file = Path(self.temp_dir) / f"{project_name}_{current_date.strftime('%Y%m%d')}.json"
        self.assertTrue(checkpoint_file.exists())
        
        # 查找恢复点
        start_date = date(2023, 5, 1)
        end_date = date(2023, 5, 31)
        resume_point = self.manager.find_resume_point(project_name, start_date, end_date)
        
        # 应该从下一天开始恢复
        expected_resume = current_date + timedelta(days=1)
        self.assertEqual(resume_point, expected_resume)
    
    def test_error_checkpoint(self):
        """测试错误检查点"""
        project_name = "test_project"
        error_date = date(2023, 5, 10)
        error_msg = "Test error message"
        
        # 保存错误检查点
        self.manager.save_error_checkpoint(project_name, error_date, error_msg)
        
        # 验证错误文件存在
        error_file = Path(self.temp_dir) / f"{project_name}_error_{error_date.strftime('%Y%m%d')}.json"
        self.assertTrue(error_file.exists())
    
    def test_no_resume_point(self):
        """测试没有恢复点的情况"""
        project_name = "nonexistent_project"
        start_date = date(2023, 5, 1)
        end_date = date(2023, 5, 31)
        
        resume_point = self.manager.find_resume_point(project_name, start_date, end_date)
        self.assertIsNone(resume_point)


class TestOptimizedSimulationEngine(unittest.TestCase):
    """优化模拟引擎测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'memory_limit_mb': 1024,
            'gc_frequency': 5,
            'checkpoint_frequency': 10,
            'enable_parallel': False,
            'checkpoint_dir': self.temp_dir
        }
        self.engine = OptimizedSimulationEngine(self.config)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_memory_monitoring(self):
        """测试内存监控功能"""
        # 测试内存使用量获取
        memory_usage = self.engine._get_memory_usage()
        self.assertIsInstance(memory_usage, float)
        self.assertGreater(memory_usage, 0)
        
        # 测试内存限制检查
        # 模拟高内存使用
        with patch.object(self.engine, '_get_memory_usage', return_value=900):
            self.assertTrue(self.engine._check_memory_limit())
        
        # 模拟正常内存使用
        with patch.object(self.engine, '_get_memory_usage', return_value=500):
            self.assertFalse(self.engine._check_memory_limit())
    
    def test_garbage_collection(self):
        """测试垃圾回收功能"""
        # 记录垃圾回收前的内存
        before_memory = self.engine._get_memory_usage()
        
        # 执行垃圾回收
        self.engine._perform_garbage_collection()
        
        # 验证垃圾回收执行（不验证具体效果，因为可能没有可回收的对象）
        after_memory = self.engine._get_memory_usage()
        self.assertIsInstance(after_memory, float)
    
    def test_state_caching(self):
        """测试状态缓存功能"""
        # 创建模拟的管理器
        mock_manager = Mock(spec=ProjectHDF5Manager)
        mock_manager.__enter__ = Mock(return_value=mock_manager)
        mock_manager.__exit__ = Mock(return_value=None)
        mock_manager.load_daily_data = Mock(return_value={'states': {'lai': 2.5, 'biomass': 1000}})
        
        project_name = "test_project"
        test_date = date(2023, 5, 10)
        
        # 第一次加载（应该从管理器加载）
        states1 = self.engine._load_previous_states_cached(mock_manager, project_name, test_date)
        self.assertEqual(states1['lai'], 2.5)
        self.assertEqual(mock_manager.load_daily_data.call_count, 1)
        
        # 第二次加载（应该从缓存加载）
        states2 = self.engine._load_previous_states_cached(mock_manager, project_name, test_date)
        self.assertEqual(states2['lai'], 2.5)
        self.assertEqual(mock_manager.load_daily_data.call_count, 1)  # 没有增加
    
    def test_parameter_extraction(self):
        """测试参数提取功能"""
        # 创建模拟的模型对象
        mock_model = Mock()
        mock_model.soilprofile = Mock()
        mock_model.soilprofile.hydraulic_conductivity = 0.5
        
        parameters = self.engine._extract_parameters_fast(mock_model)
        self.assertEqual(parameters['hydraulic_conductivity'], 0.5)
        
        # 测试没有土壤剖面的情况
        mock_model_no_soil = Mock()
        mock_model_no_soil.soilprofile = None
        
        parameters_empty = self.engine._extract_parameters_fast(mock_model_no_soil)
        self.assertEqual(parameters_empty, {})


class TestBatchSimulationManager(unittest.TestCase):
    """批量模拟管理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = {
            'enable_parallel_batch': False,
            'max_batch_workers': 2
        }
        self.manager = BatchSimulationManager(self.config)
    
    def test_parameter_combination_generation(self):
        """测试参数组合生成"""
        parameter_ranges = {
            'param1': [1, 2],
            'param2': ['a', 'b'],
            'param3': [0.1, 0.2]
        }
        
        combinations = self.manager._generate_parameter_combinations(parameter_ranges)
        
        # 应该生成 2 * 2 * 2 = 8 个组合
        self.assertEqual(len(combinations), 8)
        
        # 验证第一个组合
        first_combo = combinations[0]
        self.assertIn('param1', first_combo)
        self.assertIn('param2', first_combo)
        self.assertIn('param3', first_combo)
    
    @patch('pyahc.db.hdf5.optimization.BatchSimulationManager._run_single_simulation')
    def test_sequential_simulations(self, mock_run_single):
        """测试顺序模拟执行"""
        # 设置模拟返回值
        mock_run_single.return_value = {
            'project_name': 'test_project_sweep_000',
            'duration': 10.0,
            'statistics': {'yield': 5000},
            'success': True
        }
        
        base_config = {
            'project_name': 'test_project',
            'start_date': date(2023, 5, 1),
            'end_date': date(2023, 5, 7)
        }
        
        parameter_combinations = [
            {'param1': 1, 'param2': 'a'},
            {'param1': 2, 'param2': 'b'}
        ]
        
        results = self.manager._run_sequential_simulations(base_config, parameter_combinations)
        
        # 验证结果
        self.assertEqual(len(results), 2)
        self.assertEqual(mock_run_single.call_count, 2)
        
        # 验证参数被正确添加到结果中
        for result in results:
            self.assertIn('parameters', result)


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        config = {
            'memory_limit_mb': 1024,
            'gc_frequency': 5,
            'checkpoint_frequency': 20,
            'checkpoint_dir': self.temp_dir
        }
        
        engine = OptimizedSimulationEngine(config)
        
        # 模拟长期运行的内存使用
        initial_memory = engine._get_memory_usage()
        memory_readings = [initial_memory]
        
        # 模拟100次操作
        for i in range(100):
            # 模拟一些内存操作
            temp_data = np.random.random((1000, 1000))
            
            # 定期垃圾回收
            if i % 5 == 0:
                engine._perform_garbage_collection()
            
            current_memory = engine._get_memory_usage()
            memory_readings.append(current_memory)
            
            del temp_data
        
        # 验证内存使用没有持续增长（内存泄漏）
        final_memory = memory_readings[-1]
        memory_growth = final_memory - initial_memory
        
        # 允许一定的内存增长，但不应该超过100MB
        self.assertLess(memory_growth, 100, "Memory usage grew too much, possible memory leak")
    
    def test_performance_monitoring_overhead(self):
        """测试性能监控的开销"""
        monitor = PerformanceMonitor()
        
        # 测试没有监控的执行时间
        start_time = time.time()
        for i in range(1000):
            # 模拟一些计算
            result = sum(range(100))
        no_monitor_time = time.time() - start_time
        
        # 测试有监控的执行时间
        monitor.start_monitoring()
        start_time = time.time()
        for i in range(1000):
            # 模拟一些计算
            result = sum(range(100))
            # 记录性能（每10次记录一次）
            if i % 10 == 0:
                monitor.record_day_performance(date(2023, 5, 1), 0.001, 100.0)
        with_monitor_time = time.time() - start_time
        monitor.stop_monitoring()
        
        # 监控开销应该很小（不超过50%）
        if no_monitor_time > 0:
            overhead_ratio = (with_monitor_time - no_monitor_time) / no_monitor_time
            self.assertLess(overhead_ratio, 0.5, "Performance monitoring overhead is too high")
        else:
            # 如果基准时间为0，至少确保监控时间不会太长
            self.assertLess(with_monitor_time, 1.0, "Performance monitoring time is too high")


if __name__ == '__main__':
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    unittest.main(verbosity=2)
